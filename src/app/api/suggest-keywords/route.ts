import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const apiKey = process.env.OPENAI_API_KEY;

let openai: OpenAI | null = null;
if (apiKey) {
  openai = new OpenAI({ apiKey });
}



export async function POST(request: Request) {
  try {
    const { productName, productCategory } = await request.json();

    if (!productName) {
      return NextResponse.json({ error: 'O nome do produto é obrigatório.' }, { status: 400 });
    }

    if (!openai) {
      return NextResponse.json(
        { error: 'A API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY.' },
        { status: 500 }
      );
    }

    const prompt = `
      Para um produto com o nome "${productName}"${productCategory ? ` na categoria "${productCategory}"` : ''}, sugira 3 a 5 palavras-chave de cauda longa (long-tail keywords) para SEO, focadas no mercado português.

      As palavras-chave devem ser específicas e relevantes para quem procura ativamente este tipo de produto online.

      Responda APENAS com um objeto JSON que contém uma única chave "keywords", que é um array de strings.

      Exemplo de resposta:
      {
        "keywords": [
          "sapatilhas de corrida leves para homem",
          "melhores sapatilhas para maratona",
          "calçado de corrida com bom amortecimento"
        ]
      }
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em SEO para e-commerce em Portugal. A sua especialidade é sugerir palavras-chave de cauda longa (long-tail) relevantes para produtos específicos, usando exclusivamente português de Portugal com ortografia 100% correta. CRÍTICO: Todas as palavras-chave devem ter ortografia perfeita, acentuação adequada e usar terminologia portuguesa (não brasileira). A resposta deve ser sempre um objeto JSON com um array de strings com português impecável."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.4, // Reduzido para maior consistência ortográfica
      max_tokens: 200,
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("A resposta da API está vazia.");
    }

    const parsedContent = JSON.parse(content);
    return NextResponse.json(parsedContent);

  } catch (error) {
    console.error('Erro na API de sugestão de palavras-chave:', error);
    return NextResponse.json({ error: 'Falha ao sugerir palavras-chave.' }, { status: 500 });
  }
}
