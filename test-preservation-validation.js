/**
 * Teste Crítico de Preservação de Informações Adicionais
 * 
 * Este script testa especificamente a preservação integral das informações
 * adicionais fornecidas pelo utilizador, validando que 100% do conteúdo
 * é preservado na descrição final.
 * 
 * Usage: node test-preservation-validation.js
 * Note: Requires OPENAI_API_KEY environment variable to be set
 */

const criticalTestCases = [
  {
    name: "Smartphone Premium com Informações Críticas",
    category: "Tecnologia",
    features: ["Ecrã OLED", "5G", "Câmara 108MP"],
    keywords: ["smartphone premium", "telemóvel 5g"],
    targetAudience: "Profissionais",
    additionalInfo: "Edição limitada de apenas 500 unidades numeradas. Inclui carregador sem fios de 65W, capa de couro italiana artesanal e auriculares Bluetooth premium. Garantia estendida de 3 anos com suporte técnico 24/7. Certificado pela União Europeia para resistência à água IP68. Processador exclusivo desenvolvido em parceria com a TSMC.",
    criticalElements: [
      "Edição limitada de apenas 500 unidades numeradas",
      "carregador sem fios de 65W",
      "capa de couro italiana artesanal",
      "auriculares Bluetooth premium",
      "Garantia estendida de 3 anos",
      "suporte técnico 24/7",
      "Certificado pela União Europeia",
      "resistência à água IP68",
      "Processador exclusivo",
      "parceria com a TSMC"
    ]
  },
  {
    name: "Azeite Artesanal com Certificações",
    category: "Alimentação",
    features: ["Extra virgem", "Primeira extração", "Acidez 0.1%"],
    keywords: ["azeite português", "azeite premium"],
    targetAudience: "Chefs e gourmets",
    additionalInfo: "Produzido em olivais centenários da região de Trás-os-Montes com mais de 300 anos. Prémio Ouro no Concurso Internacional de Azeites de Madrid 2024. Colheita manual realizada exclusivamente durante o mês de outubro. Prensagem a frio em menos de 6 horas após a colheita. Engarrafado em ambiente controlado com azoto para preservar as propriedades. Certificação DOP (Denominação de Origem Protegida) e certificação biológica pela ECOCERT.",
    criticalElements: [
      "olivais centenários",
      "região de Trás-os-Montes",
      "mais de 300 anos",
      "Prémio Ouro",
      "Concurso Internacional de Azeites de Madrid 2024",
      "Colheita manual",
      "mês de outubro",
      "Prensagem a frio",
      "menos de 6 horas",
      "Engarrafado em ambiente controlado",
      "azoto",
      "Certificação DOP",
      "certificação biológica",
      "ECOCERT"
    ]
  },
  {
    name: "Ração Premium para Cães com Especificações Veterinárias",
    category: "Animais",
    features: ["Sem cereais", "Rico em proteínas", "Digestão fácil"],
    keywords: ["ração cães premium", "alimentação canina"],
    targetAudience: "Donos de cães de raça",
    additionalInfo: "Fórmula desenvolvida por veterinários da Universidade de Lisboa em colaboração com nutricionistas caninos certificados. Testada clinicamente em mais de 200 cães durante 18 meses. Aprovada pela Associação Portuguesa de Medicina Veterinária (APMV). Ingredientes 100% naturais provenientes de fornecedores certificados pela UE. Sem conservantes artificiais, corantes ou aromatizantes sintéticos. Embalagem com atmosfera modificada para preservar a frescura. Análises microbiológicas realizadas em cada lote.",
    criticalElements: [
      "veterinários da Universidade de Lisboa",
      "nutricionistas caninos certificados",
      "Testada clinicamente",
      "mais de 200 cães",
      "18 meses",
      "Associação Portuguesa de Medicina Veterinária",
      "APMV",
      "Ingredientes 100% naturais",
      "fornecedores certificados pela UE",
      "Sem conservantes artificiais",
      "corantes ou aromatizantes sintéticos",
      "atmosfera modificada",
      "Análises microbiológicas",
      "cada lote"
    ]
  }
];

async function testPreservationValidation() {
  console.log("🔍 TESTE CRÍTICO: Preservação Integral de Informações Adicionais");
  console.log("=" .repeat(80));
  
  if (!process.env.OPENAI_API_KEY) {
    console.error("❌ Erro: OPENAI_API_KEY não está definida nas variáveis de ambiente");
    return;
  }

  console.log("✅ Chave da OpenAI encontrada");
  console.log(`📝 A testar ${criticalTestCases.length} casos críticos de preservação...\n`);

  const results = [];
  let totalFailures = 0;

  for (let i = 0; i < criticalTestCases.length; i++) {
    const testCase = criticalTestCases[i];
    console.log(`🧪 Teste ${i + 1}/${criticalTestCases.length}: ${testCase.name}`);
    console.log(`📋 Informações adicionais: ${testCase.additionalInfo.length} caracteres`);
    console.log(`🎯 Elementos críticos a preservar: ${testCase.criticalElements.length}`);
    
    try {
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          productInfo: testCase
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const description = data.seoContent.wooCommerceMainDescription;
      
      // Validação detalhada de cada elemento crítico
      const descriptionLower = description.toLowerCase();
      const preservedElements = [];
      const missingElements = [];
      
      for (const element of testCase.criticalElements) {
        const elementWords = element.toLowerCase().split(/\s+/).filter(word => word.length > 2);
        const matchedWords = elementWords.filter(word => descriptionLower.includes(word));
        const matchRate = matchedWords.length / elementWords.length;
        
        if (matchRate >= 0.7) { // 70% das palavras do elemento devem estar presentes
          preservedElements.push(element);
        } else {
          missingElements.push(element);
        }
      }
      
      const preservationRate = (preservedElements.length / testCase.criticalElements.length) * 100;
      const isSuccess = preservationRate >= 90;
      
      console.log(`📊 Resultados:`);
      console.log(`   Taxa de preservação: ${preservationRate.toFixed(1)}%`);
      console.log(`   Elementos preservados: ${preservedElements.length}/${testCase.criticalElements.length}`);
      console.log(`   Elementos em falta: ${missingElements.length}`);
      
      if (isSuccess) {
        console.log(`   ✅ SUCESSO: Preservação adequada`);
      } else {
        console.log(`   ❌ FALHA: Preservação insuficiente`);
        totalFailures++;
      }
      
      if (preservedElements.length > 0) {
        console.log(`   ✅ Preservados: ${preservedElements.slice(0, 3).join(', ')}${preservedElements.length > 3 ? '...' : ''}`);
      }
      
      if (missingElements.length > 0) {
        console.log(`   ❌ Em falta: ${missingElements.slice(0, 3).join(', ')}${missingElements.length > 3 ? '...' : ''}`);
      }
      
      results.push({
        testCase: testCase.name,
        preservationRate,
        preservedCount: preservedElements.length,
        missingCount: missingElements.length,
        isSuccess,
        description: description.substring(0, 200) + "..."
      });
      
      console.log("-".repeat(80));
      
    } catch (error) {
      console.error(`❌ Erro no teste ${testCase.name}:`, error.message);
      totalFailures++;
      console.log("-".repeat(80));
    }
    
    // Delay para evitar rate limiting
    if (i < criticalTestCases.length - 1) {
      console.log("⏳ Aguardando 3 segundos...\n");
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // Relatório final
  console.log("\n📋 RELATÓRIO FINAL DE PRESERVAÇÃO");
  console.log("=" .repeat(80));
  
  const successRate = ((results.length - totalFailures) / results.length) * 100;
  const avgPreservationRate = results.reduce((sum, r) => sum + r.preservationRate, 0) / results.length;
  
  console.log(`📊 Estatísticas Gerais:`);
  console.log(`   Taxa de sucesso dos testes: ${successRate.toFixed(1)}%`);
  console.log(`   Taxa média de preservação: ${avgPreservationRate.toFixed(1)}%`);
  console.log(`   Testes com falha: ${totalFailures}/${results.length}`);
  
  console.log(`\n🎯 Resultados por Teste:`);
  results.forEach((result, index) => {
    const status = result.isSuccess ? '✅' : '❌';
    console.log(`   ${status} ${result.testCase}: ${result.preservationRate.toFixed(1)}% (${result.preservedCount}/${result.preservedCount + result.missingCount})`);
  });
  
  console.log(`\n📋 Recomendações:`);
  if (successRate < 100) {
    console.log("   ⚠️  CRÍTICO: Alguns testes falharam - revisar prompts de preservação");
  }
  if (avgPreservationRate < 90) {
    console.log("   ⚠️  CRÍTICO: Taxa de preservação abaixo do mínimo exigido (90%)");
  }
  if (totalFailures === 0 && avgPreservationRate >= 95) {
    console.log("   ✅ EXCELENTE: Todos os testes passaram com preservação superior a 95%");
  }
  
  return {
    successRate,
    avgPreservationRate,
    totalFailures,
    results
  };
}

// Executar o teste
if (require.main === module) {
  console.log("🚀 Iniciando teste crítico de preservação...\n");
  testPreservationValidation().catch(console.error);
}

module.exports = { testPreservationValidation, criticalTestCases };
