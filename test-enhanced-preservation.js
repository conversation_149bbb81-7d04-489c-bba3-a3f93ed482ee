/**
 * Teste Aprimorado de Preservação Integral de Informações Técnicas
 * 
 * Este script testa especificamente a preservação completa de informações técnicas
 * complexas, incluindo percentagens, especificações, tecnologias e benefícios.
 * 
 * Usage: node test-enhanced-preservation.js
 * Note: Requires OPENAI_API_KEY environment variable to be set
 */

const enhancedTestCases = [
  {
    name: "Ração Premium com Informações Técnicas Complexas",
    category: "Animais",
    features: ["Sem cereais", "Rico em proteínas", "Digestão fácil"],
    keywords: ["ração cães premium", "alimentação canina"],
    targetAudience: "Donos de cães de raça",
    additionalInfo: "Composição: Proteína bruta 32%, Gordura bruta 18%, Fibra bruta 3.5%, Cinza bruta 7.2%, Humidade 10%. Ingredientes principais: Carne de frango desidrata<PERSON> (28%), batata do<PERSON>, er<PERSON><PERSON>, gord<PERSON> de frango, ó<PERSON>ão (4%), glucosamina (1000mg/kg), condroitina (800mg/kg). Tecnologia de extrusão dupla a baixa temperatura preserva nutrientes. Aprovado pela FEDIAF. Sem conservantes artificiais, corantes ou aromatizantes. Enriquecido com probióticos Lactobacillus acidophilus (1x10^9 UFC/kg). Adequado para cães adultos de todas as raças.",
    criticalElements: [
      "Proteína bruta 32%",
      "Gordura bruta 18%", 
      "Fibra bruta 3.5%",
      "Cinza bruta 7.2%",
      "Humidade 10%",
      "Carne de frango desidratada (28%)",
      "óleo de salmão (4%)",
      "glucosamina (1000mg/kg)",
      "condroitina (800mg/kg)",
      "extrusão dupla",
      "baixa temperatura",
      "FEDIAF",
      "Lactobacillus acidophilus",
      "1x10^9 UFC/kg"
    ]
  },
  {
    name: "Suplemento Nutricional com Especificações Detalhadas",
    category: "Saúde",
    features: ["Vitaminas essenciais", "Minerais", "Antioxidantes"],
    keywords: ["suplemento vitamínico", "nutrição"],
    targetAudience: "Adultos ativos",
    additionalInfo: "Fórmula avançada com Vitamina C (1000mg), Vitamina D3 (2000 UI), Vitamina E (400 UI), Zinco (15mg), Selénio (200mcg), Coenzima Q10 (100mg). Tecnologia de libertação prolongada de 12 horas. Cápsulas vegetais certificadas pela Vegetarian Society. Testado por laboratórios independentes para pureza e potência. Sem glúten, lactose, açúcar adicionado. Produzido em instalações certificadas GMP. Embalagem com 60 cápsulas para 2 meses de tratamento. Absorção melhorada com complexo de bioflavonóides cítricos.",
    criticalElements: [
      "Vitamina C (1000mg)",
      "Vitamina D3 (2000 UI)",
      "Vitamina E (400 UI)",
      "Zinco (15mg)",
      "Selénio (200mcg)",
      "Coenzima Q10 (100mg)",
      "libertação prolongada de 12 horas",
      "Vegetarian Society",
      "laboratórios independentes",
      "certificadas GMP",
      "60 cápsulas",
      "2 meses",
      "bioflavonóides cítricos"
    ]
  },
  {
    name: "Smartphone com Especificações Técnicas Avançadas",
    category: "Tecnologia",
    features: ["5G", "Câmara tripla", "Ecrã OLED"],
    keywords: ["smartphone premium", "telemóvel 5g"],
    targetAudience: "Profissionais e entusiastas",
    additionalInfo: "Processador Snapdragon 8 Gen 2 (4nm), RAM 12GB LPDDR5X, Armazenamento 256GB UFS 4.0. Ecrã AMOLED 6.8\" 3200x1440px, 120Hz, 1300 nits, Gorilla Glass Victus 2. Sistema de câmaras: Principal 50MP f/1.8 OIS, Ultra-wide 12MP f/2.2 120°, Teleobjetiva 10MP f/2.4 3x zoom ótico. Bateria 5000mAh, carregamento rápido 67W, carregamento sem fios 50W, carregamento reverso 10W. Resistência IP68, conectividade Wi-Fi 7, Bluetooth 5.3, NFC, USB-C 3.2. Certificação militar MIL-STD-810H.",
    criticalElements: [
      "Snapdragon 8 Gen 2 (4nm)",
      "RAM 12GB LPDDR5X",
      "256GB UFS 4.0",
      "AMOLED 6.8\"",
      "3200x1440px",
      "120Hz",
      "1300 nits",
      "Gorilla Glass Victus 2",
      "50MP f/1.8 OIS",
      "12MP f/2.2 120°",
      "10MP f/2.4 3x zoom",
      "5000mAh",
      "67W",
      "50W",
      "10W",
      "IP68",
      "Wi-Fi 7",
      "Bluetooth 5.3",
      "USB-C 3.2",
      "MIL-STD-810H"
    ]
  }
];

async function testEnhancedPreservation() {
  console.log("🔬 TESTE APRIMORADO: Preservação Integral de Informações Técnicas");
  console.log("=" .repeat(80));
  
  if (!process.env.OPENAI_API_KEY) {
    console.error("❌ Erro: OPENAI_API_KEY não está definida");
    return;
  }

  console.log("✅ Chave da OpenAI encontrada");
  console.log(`📝 A testar ${enhancedTestCases.length} casos com informações técnicas complexas...\n`);

  const results = [];
  let totalFailures = 0;

  for (let i = 0; i < enhancedTestCases.length; i++) {
    const testCase = enhancedTestCases[i];
    console.log(`🧪 Teste ${i + 1}/${enhancedTestCases.length}: ${testCase.name}`);
    console.log(`📋 Informações técnicas: ${testCase.additionalInfo.length} caracteres`);
    console.log(`🎯 Elementos técnicos críticos: ${testCase.criticalElements.length}`);
    
    try {
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          productInfo: testCase
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const description = data.seoContent.wooCommerceMainDescription;
      
      // Validação rigorosa de elementos técnicos
      const descriptionLower = description.toLowerCase();
      const preservedElements = [];
      const missingElements = [];
      
      for (const element of testCase.criticalElements) {
        // Para elementos técnicos, verificar presença exata ou muito próxima
        const elementLower = element.toLowerCase();
        
        // Verificar se o elemento está presente de forma exata ou com pequenas variações
        let isPresent = false;
        
        if (descriptionLower.includes(elementLower)) {
          isPresent = true;
        } else {
          // Para números e percentagens, verificar com tolerância mínima
          const numberMatch = element.match(/(\d+(?:\.\d+)?)\s*(%|mg|kg|ui|mcg|mah|hz|nits|mp)/i);
          if (numberMatch) {
            const [, number, unit] = numberMatch;
            const pattern = new RegExp(`${number}\\s*${unit}`, 'i');
            if (pattern.test(description)) {
              isPresent = true;
            }
          }
        }
        
        if (isPresent) {
          preservedElements.push(element);
        } else {
          missingElements.push(element);
        }
      }
      
      const preservationRate = (preservedElements.length / testCase.criticalElements.length) * 100;
      const isSuccess = preservationRate >= 90; // Exigir 90% para elementos técnicos
      
      console.log(`📊 Resultados Técnicos:`);
      console.log(`   Taxa de preservação: ${preservationRate.toFixed(1)}%`);
      console.log(`   Elementos preservados: ${preservedElements.length}/${testCase.criticalElements.length}`);
      console.log(`   Elementos em falta: ${missingElements.length}`);
      
      // Verificar se há informações do backend
      if (data.seoContent.preservationInfo) {
        console.log(`   Taxa backend: ${data.seoContent.preservationInfo.preservationRate.toFixed(1)}%`);
      }
      
      if (isSuccess) {
        console.log(`   ✅ SUCESSO: Preservação técnica adequada`);
      } else {
        console.log(`   ❌ FALHA: Preservação técnica insuficiente`);
        totalFailures++;
      }
      
      if (preservedElements.length > 0) {
        console.log(`   ✅ Técnicos preservados: ${preservedElements.slice(0, 5).join(', ')}${preservedElements.length > 5 ? '...' : ''}`);
      }
      
      if (missingElements.length > 0) {
        console.log(`   ❌ Técnicos em falta: ${missingElements.slice(0, 5).join(', ')}${missingElements.length > 5 ? '...' : ''}`);
      }
      
      // Verificar se a descrição tem secções técnicas organizadas
      const hasTechnicalSections = /composição|especificações|tecnologia|ingredientes/i.test(description);
      console.log(`   📋 Secções técnicas organizadas: ${hasTechnicalSections ? '✅ Sim' : '❌ Não'}`);
      
      results.push({
        testCase: testCase.name,
        preservationRate,
        preservedCount: preservedElements.length,
        missingCount: missingElements.length,
        hasTechnicalSections,
        isSuccess,
        backendRate: data.seoContent.preservationInfo?.preservationRate || 0
      });
      
      console.log("-".repeat(80));
      
    } catch (error) {
      console.error(`❌ Erro no teste ${testCase.name}:`, error.message);
      totalFailures++;
      console.log("-".repeat(80));
    }
    
    // Delay para evitar rate limiting
    if (i < enhancedTestCases.length - 1) {
      console.log("⏳ Aguardando 4 segundos...\n");
      await new Promise(resolve => setTimeout(resolve, 4000));
    }
  }

  // Relatório final aprimorado
  console.log("\n📋 RELATÓRIO FINAL DE PRESERVAÇÃO TÉCNICA");
  console.log("=" .repeat(80));
  
  const successRate = ((results.length - totalFailures) / results.length) * 100;
  const avgPreservationRate = results.reduce((sum, r) => sum + r.preservationRate, 0) / results.length;
  const avgBackendRate = results.reduce((sum, r) => sum + r.backendRate, 0) / results.length;
  const technicalSectionsRate = results.filter(r => r.hasTechnicalSections).length / results.length * 100;
  
  console.log(`📊 Estatísticas Técnicas:`);
  console.log(`   Taxa de sucesso: ${successRate.toFixed(1)}%`);
  console.log(`   Taxa média de preservação (frontend): ${avgPreservationRate.toFixed(1)}%`);
  console.log(`   Taxa média de preservação (backend): ${avgBackendRate.toFixed(1)}%`);
  console.log(`   Descrições com secções técnicas: ${technicalSectionsRate.toFixed(1)}%`);
  console.log(`   Testes com falha: ${totalFailures}/${results.length}`);
  
  console.log(`\n🎯 Resultados Detalhados:`);
  results.forEach((result, index) => {
    const status = result.isSuccess ? '✅' : '❌';
    const sections = result.hasTechnicalSections ? '📋' : '❌';
    console.log(`   ${status} ${sections} ${result.testCase}: ${result.preservationRate.toFixed(1)}% (${result.preservedCount}/${result.preservedCount + result.missingCount})`);
  });
  
  console.log(`\n📋 Recomendações:`);
  if (successRate < 100) {
    console.log("   ⚠️  CRÍTICO: Melhorar prompts para preservação técnica completa");
  }
  if (avgPreservationRate < 90) {
    console.log("   ⚠️  CRÍTICO: Taxa de preservação técnica abaixo do ideal (90%)");
  }
  if (technicalSectionsRate < 80) {
    console.log("   ⚠️  Melhorar organização de secções técnicas nas descrições");
  }
  if (totalFailures === 0 && avgPreservationRate >= 95) {
    console.log("   ✅ EXCELENTE: Preservação técnica superior a 95% em todos os testes");
  }
  
  return {
    successRate,
    avgPreservationRate,
    avgBackendRate,
    technicalSectionsRate,
    totalFailures,
    results
  };
}

// Executar o teste
if (require.main === module) {
  console.log("🚀 Iniciando teste aprimorado de preservação técnica...\n");
  testEnhancedPreservation().catch(console.error);
}

module.exports = { testEnhancedPreservation, enhancedTestCases };
