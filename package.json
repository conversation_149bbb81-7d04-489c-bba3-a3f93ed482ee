{"name": "imageoptimizaerr", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test:model": "node scripts/test-model-upgrade.js", "test:formatting": "node scripts/test-formatting-fix.js"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "heic-convert": "^2.1.0", "jszip": "^3.10.1", "lucide-react": "^0.477.0", "next": "15.2.0", "next-pwa": "^5.6.0", "next-themes": "^0.4.4", "openai": "^4.86.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "sharp": "^0.33.5", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@stagewise/toolbar-next": "^0.1.2", "@tailwindcss/postcss": "^4", "@types/heic-convert": "^2.1.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "tailwindcss": "^4", "typescript": "^5"}}